# -*- coding: utf-8 -*-
"""
聚宽策略：基于沪深A股整体市净率的买卖策略
买入逻辑：沪深A股市净率2的时候开始买，1.2满仓
卖出逻辑：持仓涨50%减半，100%清仓

修正版本：
1. 修复净资产单位问题（万元转亿元）
2. 添加异常处理
3. 确保API函数正确调用
"""

def initialize(context):
    """
    初始化函数，设定基准等等
    """
    # 设定沪深300作为基准
    g.benchmark = '000300.XSHG'
    
    # 记录持仓成本
    g.position_cost = {}
    
    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003, 
                            close_commission=0.0003, min_commission=5), type='stock')
    
    # 每天运行
    run_daily(trade_logic, time='09:30')
    
    log.info("策略初始化完成")

def before_trading_start(context):
    """
    开盘前运行函数
    """
    # 获取当前A股市场整体市净率
    g.market_pb = get_a_stock_market_pb()
    log.info(f"当前A股整体市净率: {g.market_pb:.3f}")

def get_a_stock_market_pb():
    """
    获取沪深A股整体市净率 = A股总市值 / A股总净资产
    修正版本：处理单位统一问题
    """
    try:
        # 获取所有A股股票
        all_stocks = list(get_all_securities(['stock']).index)
        
        # 过滤A股股票（排除港股通、北交所等）
        a_stocks = []
        for stock in all_stocks:
            # 沪市：600、601、603、605、688开头
            # 深市：000、001、002、003、300开头  
            if stock.startswith(('000', '001', '002', '003', '300', '600', '601', '603', '605', '688')):
                a_stocks.append(stock)
        
        log.info(f"获取到A股股票总数: {len(a_stocks)}")
        
        # 分批查询，计算总市值和总净资产
        batch_size = 500
        total_market_cap = 0  # 总市值（亿元）
        total_net_assets = 0  # 总净资产（亿元）
        valid_count = 0
        
        for i in range(0, len(a_stocks), batch_size):
            batch_stocks = a_stocks[i:i+batch_size]
            
            try:
                # 查询市值和净资产数据
                q = query(
                    valuation.code,
                    valuation.market_cap,  # 总市值（亿元）
                    balance.total_owner_equities  # 股东权益总额（万元）
                ).filter(
                    valuation.code.in_(batch_stocks),
                    valuation.market_cap > 0,
                    balance.total_owner_equities > 0
                )
                
                df = get_fundamentals(q)
                
                if len(df) > 0:
                    # 统一单位：市值（亿元），净资产（元转亿元）
                    batch_market_cap = df['market_cap'].sum()  # 亿元
                    batch_net_assets = df['total_owner_equities'].sum() / 100000000  # 元转亿元（除以1亿）
                    
                    total_market_cap += batch_market_cap
                    total_net_assets += batch_net_assets
                    valid_count += len(df)
                    
                    log.info(f"批次 {i//batch_size + 1}: {len(df)}只股票, 市值: {batch_market_cap:.0f}亿, 净资产: {batch_net_assets:.0f}亿")
                    
            except Exception as e:
                log.warning(f"批次 {i//batch_size + 1} 查询失败: {str(e)}")
                continue
        
        if total_market_cap > 0 and total_net_assets > 0:
            # 计算整体市净率 = 总市值 / 总净资产
            market_pb = total_market_cap / total_net_assets
            
            log.info(f"A股整体数据 - 有效股票: {valid_count}只")
            log.info(f"A股总市值: {total_market_cap:.0f}亿元")
            log.info(f"A股总净资产: {total_net_assets:.0f}亿元") 
            log.info(f"A股整体市净率: {market_pb:.3f}")
            
            return market_pb
        else:
            log.warning("无法获取A股总体数据，使用默认值")
            return 1.8
            
    except Exception as e:
        log.error(f"获取A股整体市净率失败: {str(e)}")
        return 1.8

def trade_logic(context):
    """
    主要交易逻辑
    """
    # 检查卖出条件
    check_sell_conditions(context)
    
    # 检查买入条件
    check_buy_conditions(context)

def check_sell_conditions(context):
    """
    检查卖出条件：持仓涨50%减半，100%清仓
    """
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        if position.total_amount > 0:
            # 获取持仓成本
            if stock not in g.position_cost:
                g.position_cost[stock] = position.avg_cost
            
            cost_price = g.position_cost[stock]
            current_price = get_current_data()[stock].last_price
            
            # 计算收益率
            return_rate = (current_price - cost_price) / cost_price
            
            if return_rate >= 1.0:  # 涨幅100%，清仓
                order_target_percent(stock, 0)
                log.info(f"清仓 {stock}，收益率: {return_rate:.2%}")
                if stock in g.position_cost:
                    del g.position_cost[stock]
                    
            elif return_rate >= 0.5:  # 涨幅50%，减半
                current_percent = position.value / context.portfolio.total_value
                target_percent = current_percent * 0.5
                order_target_percent(stock, target_percent)
                log.info(f"减半持仓 {stock}，收益率: {return_rate:.2%}")

def check_buy_conditions(context):
    """
    检查买入条件：市净率2的时候开始买，1.2满仓
    """
    market_pb = g.market_pb
    
    # 根据市净率确定目标仓位
    if market_pb <= 1.2:
        target_position = 0.95  # 满仓（留5%现金）
    elif market_pb <= 2.0:
        # 线性插值：市净率从2.0到1.2，仓位从0到95%
        target_position = 0.95 * (2.0 - market_pb) / (2.0 - 1.2)
    else:
        target_position = 0  # 不买入
    
    log.info(f"A股市净率: {market_pb:.3f}, 目标仓位: {target_position:.1%}")
    
    if target_position > 0:
        # 获取当前总仓位
        current_position = (context.portfolio.total_value - context.portfolio.available_cash) / context.portfolio.total_value
        
        if current_position < target_position * 0.95:  # 允许5%的误差
            # 需要加仓
            buy_stocks(context, target_position)

def buy_stocks(context, target_position):
    """
    买入股票
    """
    # 获取沪深300成分股作为股票池
    stock_pool = get_index_stocks('000300.XSHG')
    
    # 过滤掉ST股票和停牌股票
    current_data = get_current_data()
    filtered_stocks = []
    for stock in stock_pool:
        if (not current_data[stock].is_st and 
            not current_data[stock].paused and
            current_data[stock].last_price > 0):
            filtered_stocks.append(stock)
    
    if not filtered_stocks:
        return
    
    # 选择市净率较低的股票
    stock_pb_list = []
    for stock in filtered_stocks[:100]:  # 限制查询数量
        try:
            q = query(valuation.pb_ratio).filter(valuation.code == stock)
            df = get_fundamentals(q)
            if len(df) > 0 and df.iloc[0]['pb_ratio'] > 0:
                stock_pb_list.append((stock, df.iloc[0]['pb_ratio']))
        except:
            continue
    
    # 按市净率排序，选择前20只
    stock_pb_list.sort(key=lambda x: x[1])
    selected_stocks = [item[0] for item in stock_pb_list[:20]]
    
    if selected_stocks:
        # 计算每只股票的目标权重
        stock_weight = target_position / len(selected_stocks)
        
        # 买入股票
        for stock in selected_stocks:
            try:
                current_percent = context.portfolio.positions[stock].value / context.portfolio.total_value
                if current_percent < stock_weight * 0.9:  # 允许10%误差
                    order_target_percent(stock, stock_weight)
                    # 记录买入成本
                    if stock not in g.position_cost:
                        g.position_cost[stock] = current_data[stock].last_price
                    log.info(f"买入 {stock}，目标权重: {stock_weight:.2%}")
            except Exception as e:
                log.warning(f"买入 {stock} 失败: {str(e)}")
                continue

def after_trading_end(context):
    """
    收盘后运行函数
    """
    # 记录当天的仓位信息
    positions = len([s for s in context.portfolio.positions if context.portfolio.positions[s].total_amount > 0])
    total_value = context.portfolio.total_value
    available_cash = context.portfolio.available_cash
    position_ratio = (total_value - available_cash) / total_value
    
    log.info(f"收盘总结 - 持仓股票数: {positions}, 仓位: {position_ratio:.1%}")
    log.info(f"总资产: {total_value:.2f}, 可用资金: {available_cash:.2f}")
    log.info(f"A股市净率: {g.market_pb:.3f}")
