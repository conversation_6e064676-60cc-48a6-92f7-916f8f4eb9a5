# -*- coding: utf-8 -*-
"""
聚宽策略：基于市净率的买卖策略
买入逻辑：沪深A股市净率2的时候开始买，1.2满仓
卖出逻辑：持仓涨50%减半，100%清仓
"""

import jqdata
import pandas as pd
import numpy as np

def initialize(context):
    """
    初始化函数，设定基准等等
    """
    # 设定沪深300作为基准
    g.benchmark = '000300.XSHG'
    
    # 设定股票池为沪深A股
    g.stock_pool = []
    
    # 记录持仓成本
    g.position_cost = {}
    
    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003, 
                            close_commission=0.0003, min_commission=5), type='stock')
    
    # 每天运行
    run_daily(trade_logic, time='09:30')
    
    log.info("策略初始化完成")

def before_trading_start(context):
    """
    开盘前运行函数
    """
    # 获取当前A股市场整体市净率
    g.market_pb = get_market_pb()
    log.info(f"当前市场市净率: {g.market_pb:.2f}")

def get_market_pb():
    """
    获取沪深A股整体市净率
    """
    # 获取沪深300成分股作为市场代表
    stocks = get_index_stocks('000300.XSHG')
    
    # 获取基本面数据
    q = query(
        valuation.code,
        valuation.pb_ratio
    ).filter(
        valuation.code.in_(stocks),
        valuation.pb_ratio > 0  # 过滤掉负值
    )
    
    df = get_fundamentals(q)
    
    if len(df) > 0:
        # 计算加权平均市净率
        market_pb = df['pb_ratio'].mean()
        return market_pb
    else:
        return 2.0  # 默认值

def get_stock_pool(context):
    """
    获取股票池
    """
    # 获取沪深300成分股
    stocks = get_index_stocks('000300.XSHG')
    
    # 过滤掉ST股票和停牌股票
    current_data = get_current_data()
    stocks = [stock for stock in stocks 
              if not current_data[stock].is_st 
              and not current_data[stock].paused]
    
    return stocks[:50]  # 限制股票数量，避免过度分散

def trade_logic(context):
    """
    主要交易逻辑
    """
    current_data = get_current_data()
    
    # 检查卖出条件
    check_sell_conditions(context)
    
    # 检查买入条件
    check_buy_conditions(context)

def check_sell_conditions(context):
    """
    检查卖出条件：持仓涨50%减半，100%清仓
    """
    for stock in context.portfolio.positions:
        position = context.portfolio.positions[stock]
        if position.total_amount > 0:
            # 获取持仓成本
            if stock not in g.position_cost:
                g.position_cost[stock] = position.avg_cost
            
            cost_price = g.position_cost[stock]
            current_price = get_current_data()[stock].last_price
            
            # 计算收益率
            return_rate = (current_price - cost_price) / cost_price
            
            if return_rate >= 1.0:  # 涨幅100%，清仓
                order_target_percent(stock, 0)
                log.info(f"清仓 {stock}，收益率: {return_rate:.2%}")
                if stock in g.position_cost:
                    del g.position_cost[stock]
                    
            elif return_rate >= 0.5:  # 涨幅50%，减半
                current_percent = context.portfolio.positions[stock].value / context.portfolio.total_value
                target_percent = current_percent * 0.5
                order_target_percent(stock, target_percent)
                log.info(f"减半持仓 {stock}，收益率: {return_rate:.2%}")

def check_buy_conditions(context):
    """
    检查买入条件：市净率2的时候开始买，1.2满仓
    """
    market_pb = g.market_pb
    
    # 根据市净率确定仓位
    if market_pb <= 1.2:
        target_position = 0.95  # 满仓（留5%现金）
    elif market_pb <= 2.0:
        # 线性插值：市净率从2.0到1.2，仓位从0到95%
        target_position = 0.95 * (2.0 - market_pb) / (2.0 - 1.2)
    else:
        target_position = 0  # 不买入
    
    log.info(f"市净率: {market_pb:.2f}, 目标仓位: {target_position:.2%}")
    
    if target_position > 0:
        # 获取当前总仓位
        current_position = (context.portfolio.total_value - context.portfolio.available_cash) / context.portfolio.total_value
        
        if current_position < target_position:
            # 需要加仓
            buy_stocks(context, target_position)

def buy_stocks(context, target_position):
    """
    买入股票
    """
    # 获取股票池
    stock_pool = get_stock_pool(context)
    
    if not stock_pool:
        return
    
    # 计算每只股票的目标权重
    stock_weight = target_position / len(stock_pool)
    
    # 选择市净率较低的股票
    stock_pb_data = []
    for stock in stock_pool:
        q = query(valuation.pb_ratio).filter(valuation.code == stock)
        df = get_fundamentals(q)
        if len(df) > 0 and df.iloc[0]['pb_ratio'] > 0:
            stock_pb_data.append((stock, df.iloc[0]['pb_ratio']))
    
    # 按市净率排序，选择市净率较低的股票
    stock_pb_data.sort(key=lambda x: x[1])
    selected_stocks = [item[0] for item in stock_pb_data[:20]]  # 选择前20只
    
    # 买入股票
    for stock in selected_stocks:
        current_percent = context.portfolio.positions[stock].value / context.portfolio.total_value
        if current_percent < stock_weight:
            order_target_percent(stock, stock_weight)
            # 记录买入成本
            if stock not in g.position_cost:
                g.position_cost[stock] = get_current_data()[stock].last_price
            log.info(f"买入 {stock}，目标权重: {stock_weight:.2%}")

def after_trading_end(context):
    """
    收盘后运行函数
    """
    # 记录当天的仓位信息
    positions = len([s for s in context.portfolio.positions if context.portfolio.positions[s].total_amount > 0])
    log.info(f"今日收盘，持仓股票数: {positions}")
    log.info(f"总资产: {context.portfolio.total_value:.2f}")
    log.info(f"可用资金: {context.portfolio.available_cash:.2f}")
